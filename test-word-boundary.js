// Test script for word boundary matching in Boolean AND search

function performBooleanAndSearch(jobTitle, searchKeyword) {
    if (!jobTitle || !searchKeyword) {
        return false;
    }

    // Convert both to lowercase for case-insensitive comparison
    const titleLower = jobTitle.toLowerCase();
    const keywordLower = searchKeyword.toLowerCase();

    // Split the search keyword into individual terms
    const searchTerms = keywordLower.split(/\s+/).filter(term => term.length > 0);

    // Check if all terms are present in the job title as complete words (Boolean AND)
    return searchTerms.every(term => {
        // Create regex pattern for word boundary matching
        const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const wordBoundaryPattern = new RegExp(`\\b${escapedTerm}\\b`, 'i');
        
        // First check if the term exists as a complete word
        if (wordBoundaryPattern.test(titleLower)) {
            return true;
        }

        // If the term contains hyphens, also check without hyphens
        if (term.includes('-') || term.includes('‑')) {
            const termWithoutHyphens = term.replace(/[-‑]/g, ' ');
            // Split the unhyphenated term and check if all parts are present as complete words
            const subTerms = termWithoutHyphens.split(/\s+/).filter(subTerm => subTerm.length > 0);
            return subTerms.every(subTerm => {
                const escapedSubTerm = subTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const subWordPattern = new RegExp(`\\b${escapedSubTerm}\\b`, 'i');
                return subWordPattern.test(titleLower);
            });
        }

        // If the term doesn't contain hyphens, also check if it matches a hyphenated version in the title
        // This handles cases where the search term is "bolt on" but title has "bolt-on"
        const hyphenatedVersions = [
            term.replace(/\s+/g, '-'),
            term.replace(/\s+/g, '‑')
        ];
        return hyphenatedVersions.some(hyphenatedTerm => {
            const escapedHyphenTerm = hyphenatedTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const hyphenPattern = new RegExp(`\\b${escapedHyphenTerm}\\b`, 'i');
            return hyphenPattern.test(titleLower);
        });
    });
}

// Test cases for word boundary matching
const testCases = [
    {
        jobTitle: "Accountant",
        searchKeyword: "CCO",
        expected: false,
        description: "CCO should not match Accountant (substring issue)"
    },
    {
        jobTitle: "Chief Compliance Officer",
        searchKeyword: "CCO",
        expected: false,
        description: "CCO should not match Chief Compliance Officer (abbreviation vs full title)"
    },
    {
        jobTitle: "CCO and Founder",
        searchKeyword: "CCO",
        expected: true,
        description: "CCO should match CCO as complete word"
    },
    {
        jobTitle: "Senior CCO",
        searchKeyword: "CCO",
        expected: true,
        description: "CCO should match when it's a complete word in title"
    },
    {
        jobTitle: "CEO",
        searchKeyword: "CEO",
        expected: true,
        description: "CEO exact match"
    },
    {
        jobTitle: "Chief Executive Officer",
        searchKeyword: "CEO",
        expected: false,
        description: "CEO should not match Chief Executive Officer"
    },
    {
        jobTitle: "Vice President",
        searchKeyword: "President",
        expected: true,
        description: "President should match as complete word"
    },
    {
        jobTitle: "Presidential Advisor",
        searchKeyword: "President",
        expected: false,
        description: "President should not match Presidential (substring)"
    },
    {
        jobTitle: "Managing Partner",
        searchKeyword: "Partner",
        expected: true,
        description: "Partner should match as complete word"
    },
    {
        jobTitle: "Partnership Director",
        searchKeyword: "Partner",
        expected: false,
        description: "Partner should not match Partnership (substring)"
    },
    {
        jobTitle: "Bolt-on Acquisition Specialist",
        searchKeyword: "Bolt‑on",
        expected: true,
        description: "Hyphenated terms should match with word boundaries"
    },
    {
        jobTitle: "Bolton Strategy Director",
        searchKeyword: "Bolt‑on",
        expected: false,
        description: "Hyphenated search should not match combined word"
    },
    {
        jobTitle: "Buy and Build Strategy",
        searchKeyword: "Buy‑and‑Build",
        expected: true,
        description: "Multi-hyphen search should match spaced equivalent"
    },
    {
        jobTitle: "Buyer and Builder",
        searchKeyword: "Buy‑and‑Build",
        expected: false,
        description: "Should not match similar but different words"
    },
    {
        jobTitle: "VP Sales Director",
        searchKeyword: "VP Sales",
        expected: true,
        description: "Multi-word search should match as complete words"
    },
    {
        jobTitle: "SVP Sales Manager",
        searchKeyword: "VP Sales",
        expected: false,
        description: "VP should not match SVP (substring)"
    }
];

console.log("Testing word boundary matching in Boolean AND search:\n");

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
    const result = performBooleanAndSearch(testCase.jobTitle, testCase.searchKeyword);
    const success = result === testCase.expected;
    
    console.log(`Test ${index + 1}: ${testCase.description}`);
    console.log(`  Job Title: "${testCase.jobTitle}"`);
    console.log(`  Search Keyword: "${testCase.searchKeyword}"`);
    console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
    console.log(`  Result: ${success ? 'PASS' : 'FAIL'}`);
    console.log('');
    
    if (success) {
        passed++;
    } else {
        failed++;
    }
});

console.log(`\nTest Summary:`);
console.log(`Passed: ${passed}`);
console.log(`Failed: ${failed}`);
console.log(`Total: ${testCases.length}`);

if (failed === 0) {
    console.log('\n✅ All word boundary tests passed!');
} else {
    console.log('\n❌ Some tests failed. Please review the implementation.');
}

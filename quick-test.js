// Quick test to demonstrate Boolean AND search behavior

function performBooleanAndSearch(jobTitle, searchKeyword) {
    if (!jobTitle || !searchKeyword) {
        return false;
    }

    // Convert both to lowercase for case-insensitive comparison
    const titleLower = jobTitle.toLowerCase();
    const keywordLower = searchKeyword.toLowerCase();

    // Split the search keyword into individual terms
    const searchTerms = keywordLower.split(/\s+/).filter(term => term.length > 0);

    // Check if all terms are present in the job title (Boolean AND)
    return searchTerms.every(term => titleLower.includes(term));
}

// Test cases to demonstrate the behavior
const testCases = [
    {
        jobTitle: "Business Owner and Entrepreneur",
        searchKeyword: "Business Owner",
        description: "Normal order: Business Owner"
    },
    {
        jobTitle: "Owner of Business",
        searchKeyword: "Business Owner", 
        description: "Different order: Owner of Business"
    },
    {
        jobTitle: "Small Business Owner",
        searchKeyword: "Business Owner",
        description: "Terms separated: Small Business Owner"
    },
    {
        jobTitle: "Owner and Business Partner",
        searchKeyword: "Business Owner",
        description: "Both terms present: Owner and Business Partner"
    },
    {
        jobTitle: "Restaurant Owner",
        searchKeyword: "Business Owner",
        description: "Missing 'business' term: Restaurant Owner"
    }
];

console.log("Testing Boolean AND search with different word orders:\n");

testCases.forEach((testCase, index) => {
    const result = performBooleanAndSearch(testCase.jobTitle, testCase.searchKeyword);
    
    console.log(`Test ${index + 1}: ${testCase.description}`);
    console.log(`  Job Title: "${testCase.jobTitle}"`);
    console.log(`  Search Keyword: "${testCase.searchKeyword}"`);
    console.log(`  Result: ${result ? 'MATCH' : 'NO MATCH'}`);
    
    // Show the breakdown
    const titleLower = testCase.jobTitle.toLowerCase();
    const searchTerms = testCase.searchKeyword.toLowerCase().split(/\s+/);
    console.log(`  Terms to find: [${searchTerms.join(', ')}]`);
    console.log(`  Found in title: [${searchTerms.map(term => titleLower.includes(term) ? `✓${term}` : `✗${term}`).join(', ')}]`);
    console.log('');
});

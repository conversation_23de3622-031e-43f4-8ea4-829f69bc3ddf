// Test script for hyphen handling in Boolean AND search

function performBooleanAndSearch(jobTitle, searchKeyword) {
    if (!jobTitle || !searchKeyword) {
        return false;
    }

    // Convert both to lowercase for case-insensitive comparison
    const titleLower = jobTitle.toLowerCase();
    const keywordLower = searchKeyword.toLowerCase();

    // Split the search keyword into individual terms
    const searchTerms = keywordLower.split(/\s+/).filter(term => term.length > 0);

    // Check if all terms are present in the job title (Boolean AND)
    return searchTerms.every(term => {
        // First check if the term exists as-is
        if (titleLower.includes(term)) {
            return true;
        }

        // If the term contains hyphens, also check without hyphens
        if (term.includes('-') || term.includes('‑')) {
            const termWithoutHyphens = term.replace(/[-‑]/g, ' ');
            // Split the unhyphenated term and check if all parts are present
            const subTerms = termWithoutHyphens.split(/\s+/).filter(subTerm => subTerm.length > 0);
            return subTerms.every(subTerm => titleLower.includes(subTerm));
        }

        // If the term doesn't contain hyphens, also check if it matches a hyphenated version in the title
        // This handles cases where the search term is "bolt on" but title has "bolt-on"
        const hyphenatedVersions = [
            term.replace(/\s+/g, '-'),
            term.replace(/\s+/g, '‑')
        ];
        return hyphenatedVersions.some(hyphenatedTerm => titleLower.includes(hyphenatedTerm));
    });
}

// Test cases for hyphen handling
const testCases = [
    {
        jobTitle: "Bolt-on Acquisition Specialist",
        searchKeyword: "Bolt‑on",
        expected: true,
        description: "Hyphenated search term matches hyphenated title (different hyphen types)"
    },
    {
        jobTitle: "Bolt on Strategy Director",
        searchKeyword: "Bolt‑on",
        expected: true,
        description: "Hyphenated search term matches spaced title"
    },
    {
        jobTitle: "Bolt-on Investment Manager",
        searchKeyword: "Bolt on",
        expected: true,
        description: "Spaced search term matches hyphenated title"
    },
    {
        jobTitle: "Strategic Bolt on Acquisitions",
        searchKeyword: "Bolt on",
        expected: true,
        description: "Spaced search term matches spaced title"
    },
    {
        jobTitle: "Buy‑and‑Build Strategy Lead",
        searchKeyword: "Buy‑and‑Build",
        expected: true,
        description: "Multi-hyphen exact match"
    },
    {
        jobTitle: "Buy and Build Strategy Lead",
        searchKeyword: "Buy‑and‑Build",
        expected: true,
        description: "Multi-hyphen search matches spaced title"
    },
    {
        jobTitle: "Buy-and-Build Strategy Lead",
        searchKeyword: "Buy and Build",
        expected: true,
        description: "Spaced search matches multi-hyphen title"
    },
    {
        jobTitle: "Roll‑up Specialist",
        searchKeyword: "Roll‑up",
        expected: true,
        description: "Single hyphen exact match"
    },
    {
        jobTitle: "Roll up Strategy",
        searchKeyword: "Roll‑up",
        expected: true,
        description: "Hyphen search matches spaced title"
    },
    {
        jobTitle: "Rollup Director",
        searchKeyword: "Roll‑up",
        expected: false,
        description: "Hyphen search should not match combined word"
    },
    {
        jobTitle: "Self‑Funded Searcher",
        searchKeyword: "Self‑Funded",
        expected: true,
        description: "Self-Funded exact match"
    },
    {
        jobTitle: "Self Funded Business Owner",
        searchKeyword: "Self‑Funded",
        expected: true,
        description: "Hyphen search matches spaced title"
    },
    {
        jobTitle: "Independent Sponsor",
        searchKeyword: "Indie Sponsor",
        expected: false,
        description: "Different words should not match"
    }
];

console.log("Testing hyphen handling in Boolean AND search:\n");

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
    const result = performBooleanAndSearch(testCase.jobTitle, testCase.searchKeyword);
    const success = result === testCase.expected;
    
    console.log(`Test ${index + 1}: ${testCase.description}`);
    console.log(`  Job Title: "${testCase.jobTitle}"`);
    console.log(`  Search Keyword: "${testCase.searchKeyword}"`);
    console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
    console.log(`  Result: ${success ? 'PASS' : 'FAIL'}`);
    console.log('');
    
    if (success) {
        passed++;
    } else {
        failed++;
    }
});

console.log(`\nTest Summary:`);
console.log(`Passed: ${passed}`);
console.log(`Failed: ${failed}`);
console.log(`Total: ${testCases.length}`);

if (failed === 0) {
    console.log('\n✅ All hyphen tests passed!');
} else {
    console.log('\n❌ Some tests failed. Please review the implementation.');
}

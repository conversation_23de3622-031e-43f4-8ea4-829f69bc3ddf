// Test script for the Boolean AND search functionality

// Define the search function directly for testing
function performBooleanAndSearch(jobTitle, searchKeyword) {
    if (!jobTitle || !searchKeyword) {
        return false;
    }

    // Convert both to lowercase for case-insensitive comparison
    const titleLower = jobTitle.toLowerCase();
    const keywordLower = searchKeyword.toLowerCase();

    // Split the search keyword into individual terms
    const searchTerms = keywordLower.split(/\s+/).filter(term => term.length > 0);

    // Check if all terms are present in the job title (Boolean AND)
    return searchTerms.every(term => titleLower.includes(term));
}

// Test cases for the Boolean AND search
const testCases = [
    {
        jobTitle: "Chief Executive Officer",
        searchKeyword: "CEO",
        expected: false,
        description: "CEO abbreviation not found in full title"
    },
    {
        jobTitle: "Vice President of Sales",
        searchKeyword: "Vice President",
        expected: true,
        description: "Multi-word match"
    },
    {
        jobTitle: "Senior Vice President of Marketing",
        searchKeyword: "VP Marketing",
        expected: false,
        description: "VP abbreviation not matching Vice President"
    },
    {
        jobTitle: "VP Marketing Director",
        searchKeyword: "VP Marketing",
        expected: true,
        description: "VP abbreviation match"
    },
    {
        jobTitle: "Chief Financial Officer and CFO",
        searchKeyword: "CFO",
        expected: true,
        description: "CFO match"
    },
    {
        jobTitle: "Managing Partner at Law Firm",
        searchKeyword: "Managing Partner",
        expected: true,
        description: "Managing Partner match"
    },
    {
        jobTitle: "Partner at Investment Firm",
        searchKeyword: "Managing Partner",
        expected: false,
        description: "Partial match should fail (Boolean AND)"
    },
    {
        jobTitle: "CHIEF EXECUTIVE OFFICER",
        searchKeyword: "CEO",
        expected: false,
        description: "CEO abbreviation not found in full title (case insensitive)"
    },
    {
        jobTitle: "CEO and Founder",
        searchKeyword: "CEO",
        expected: true,
        description: "Case insensitive match"
    },
    {
        jobTitle: "Business Owner and Entrepreneur",
        searchKeyword: "Business Owner",
        expected: true,
        description: "Business Owner match"
    },
    {
        jobTitle: "Owner of Business",
        searchKeyword: "Business Owner",
        expected: true,
        description: "Boolean AND finds both terms regardless of order"
    },
    {
        jobTitle: "Private Equity Partner",
        searchKeyword: "Private Equity",
        expected: true,
        description: "Private Equity match"
    },
    {
        jobTitle: "",
        searchKeyword: "CEO",
        expected: false,
        description: "Empty job title"
    },
    {
        jobTitle: "CEO",
        searchKeyword: "",
        expected: false,
        description: "Empty search keyword"
    }
];

console.log("Testing Boolean AND search functionality...\n");

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
    const result = performBooleanAndSearch(testCase.jobTitle, testCase.searchKeyword);
    const success = result === testCase.expected;
    
    console.log(`Test ${index + 1}: ${testCase.description}`);
    console.log(`  Job Title: "${testCase.jobTitle}"`);
    console.log(`  Search Keyword: "${testCase.searchKeyword}"`);
    console.log(`  Expected: ${testCase.expected}, Got: ${result}`);
    console.log(`  Result: ${success ? 'PASS' : 'FAIL'}`);
    console.log('');
    
    if (success) {
        passed++;
    } else {
        failed++;
    }
});

console.log(`\nTest Summary:`);
console.log(`Passed: ${passed}`);
console.log(`Failed: ${failed}`);
console.log(`Total: ${testCases.length}`);

if (failed === 0) {
    console.log('\n✅ All tests passed!');
} else {
    console.log('\n❌ Some tests failed. Please review the implementation.');
}

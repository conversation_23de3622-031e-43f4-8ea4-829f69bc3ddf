const { chunk } = require("lodash");
const murmur3 = require("murmurhash3js");
const BaseResumeSelector = require("../base");
const KafkaHandler = require("../../helpers/kafka-handler");
const logger = require("../../logger");
const { argv } = require("../../helpers/argv");
const config = require("../../config");
const { safeJsonObj } = require("../../helpers/misc");
const postgres = require("../../postgres-cr");
const postgresDuke = require("../../postgres-duke");

const searchKeywords = [
    "CEO",
    "President",
    "Vice President",
    "Owner",
    "Business Owner",
    "Founder",
    "Cofounder",
    "Managing Partner",
    "Senior Partner",
    "Partner",
    "Principal",
    "General Partner",
    "Executive Chair",
    "Chairman",
    "Chairwoman",
    "Board Chair",
    "Board Director",
    "CFO",
    "COO",
    "CRO",
    "CMO",
    "CTO",
    "CIO",
    "CSO",
    "CGO",
    "CPO",
    "CDO",
    "CLO",
    "CCO",
    "VP Sales",
    "VP Revenue",
    "VP Finance",
    "VP Marketing",
    "VP Growth",
    "VP Product",
    "VP Operations",
    "Head Sales",
    "Head Growth",
    "Head Revenue",
    "Head Finance",
    "Head Marketing",
    "Head Product",
    "Head Strategy",
    "Strategy Director",
    "Finance Director",
    "Ops Director",
    "Growth Director",
    "Revenue Director",
    "Corp Dev",
    "Acquisition VP",
    "Acquisition Head",
    "Buy Side",
    "Buyout",
    "Buyside",
    "Rollup",
    "Holdco",
    "Search Fund",
    "Searcher",
    "ETA",
    "Acquirer",
    "Acquisition",
    "Private Equity",
    "Independent Sponsor",
    "Venture Partner",
    "Venture Principal",
    "Investment Partner",
    "Fund Manager",
    "Portfolio Partner",
    "PE Partner",
    "PE Principal",
    "Equity Partner",
    "Deal Partner",
    "Banking Partner",
    "IB Director",
    "Capital Partner",
    "Deal Banker",
    "Mergers Director",
    "GovCon Partner",
    "GovCon Director",
    "GovCon Advisor",
    "Contracts VP",
    "Capture Director",
    "Insurance Partner",
    "Risk Director",
    "Underwriting VP",
    "Staffing Partner",
    "Staffing Director",
    "Recruiting Director",
    "PR Partner",
    "PR Director",
    "Comms VP",
    "ABL Partner",
    "Lending Head",
    "Realty Partner",
    "CRE Director",
    "Property Head",
    "Supply Lead",
    "Logistics VP",
    "Sourcing Director",
    "Procurement VP",
    "Franchise VP",
    "Franchise Director",
    "Fintech Partner",
    "Fintech VP",
    "BD Partner",
    "SDR VP",
    "SDR Director",
    "Channel VP",
    "SMB Acquirer",
    "Holdco Operator",
    "Rollup Strategist",
    "Aggregator",
    "Consolidator",
    "Consolidation",
    "Bolt‑on",
    "Platform",
    "Micro‑PE",
    "MicroPE",
    "SBA 7a",
    "Self‑Funded",
    "Indie Sponsor",
    "Roll‑up",
    "Growth Equity",
    "LBO",
    "Buy‑and‑Build",
    "Continuation",
    "Co‑Investment",
    "Fundless",
    "SearchCo",
    "SMB Acquisition",
    "Small‑Cap",
    "Lower‑Mid",
    "Roll‑in",
    "Thesis",
    "Continuity",
    "Add‑on"
];


class Selector extends BaseResumeSelector {
    constructor(config) {
        super(config);
        // this.queryLimit = argv.limit;
        this.queryLimit = 10;
        this.chunkSize = argv.chunk;
    }

    /**
     * Performs Boolean AND search on job title using search keywords
     * @param {string} jobTitle - The job title to search in
     * @param {string} searchKeyword - The search keyword to look for
     * @returns {boolean} - True if all terms in the keyword are found in the job title
     */
    performBooleanAndSearch(jobTitle, searchKeyword) {
        if (!jobTitle || !searchKeyword) {
            return false;
        }

        // Convert both to lowercase for case-insensitive comparison
        const titleLower = jobTitle.toLowerCase();
        const keywordLower = searchKeyword.toLowerCase();

        // Split the search keyword into individual terms
        const searchTerms = keywordLower.split(/\s+/).filter(term => term.length > 0);

        // Check if all terms are present in the job title (Boolean AND)
        return searchTerms.every(term => titleLower.includes(term));
    }

    async process() {
        try {
            this.initBaseLoggerData();

            await this.processByFlow();
        } catch (err) {
            logger.error("Error has happened", {
                errString: err.toString(),
                errStackString: err.stack
            });
            throw err;
        }
    }

    async processByFlow() {
        try {
            let prevResumeId = '';
            let count = 0;
            let usersPresent = true;

            // do {
                logger.info(`The start resume id is ${prevResumeId}`);

                const data = await this.models.getCRDataPG().getUserTitlesByResumeId(prevResumeId, this.queryLimit);

                count += data.length;

                logger.info("Selected users from pg", {
                    ...this.loggerData,
                    prevResumeId,
                    count,
                });

                // if (!data.length) {
                //     usersPresent = false;
                //     break;
                // }

                prevResumeId = data[data.length - 1].cr_resume_id;
                const chunks = chunk(data, this.chunkSize);

                await Promise.all(
                    chunks.map(async users => {
                        let messagesToPG = [];

                        for (const user of users) {
                            const { cr_resume_id, job_title, email } = user;

                            // Skip if any required field is missing
                            if (!cr_resume_id || !job_title || !email) {
                                continue;
                            }

                            // Search through all keywords to find matches
                            for (const searchKeyword of searchKeywords) {
                                if (this.performBooleanAndSearch(job_title, searchKeyword)) {
                                    messagesToPG.push({
                                        email: email,
                                        search_keyword: searchKeyword,
                                        job_title: job_title,
                                        resume_id: cr_resume_id
                                    });
                                }
                            }
                        }

                        // Save matches to database if any found
                        if (messagesToPG.length > 0) {
                            try {
                                await this.models.getDukeDataPG().insertSearchCanvasUsers(messagesToPG);

                                logger.info("Search results saved to pg", {
                                    count: messagesToPG.length,
                                    ...this.loggerData
                                });
                            } catch (error) {
                                logger.error("Error saving search results to pg", {
                                    errString: error.toString(),
                                    errStackString: error.stack,
                                    count: messagesToPG.length,
                                    ...this.loggerData
                                });
                            }
                        }

                        messagesToPG = [];
                    }),
                );

                logger.info("Users are processed", {
                    count: count,
                    ...this.loggerData
                });
            // } while (usersPresent);

            logger.info("Got all users", {
                ...this.loggerData,
                prevResumeId,
                count
            });
        } catch (err) {
            logger.error("Error has happened in processing by flow", {
                errString: err.toString(),
                errStackString: err.stack
            });
            throw err;
        }
    }
}

module.exports = Selector;

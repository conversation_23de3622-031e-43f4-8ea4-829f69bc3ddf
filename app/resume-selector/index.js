const { createObjectForProvider } = require("../helpers/misc");

const BaseResumeSelector = require("./base");
const MixrankResumeSelector = require("./mixrank");
const MixrankResumeSelectorV3 = require("./mixrank-v3");
const gptProfileSelector = require("./gpt-profile");
const CareerbuilderResumeSelector = require("./careerbuilder");
const ExploriumResumeSelector = require("./explorium");
const MixrankSimpleResumeSelector = require("./mixrank-simple");
const SearchesCanvasResumeSelector = require("./searches-canvas");

const resumeSelectors = {
    mixrank: {
        index: MixrankResumeSelector
    },
    gpt_profile: {
        index: gptProfileSelector
    },
    careerbuilder: {
        index: CareerbuilderResumeSelector
    },
    explorium: {
        index: ExploriumResumeSelector
    },
    mixrank_v3: {
        index: MixrankResumeSelectorV3
    },
    mixrank_simple: {
        index: MixrankSimpleResumeSelector
    },
    searches_canvas: {
        index: SearchesCanvasResumeSelector
    },
    index: BaseResumeSelector
};

const init = (params) => {
    return createObjectForProvider(resumeSelectors, params);
};

module.exports = { init };

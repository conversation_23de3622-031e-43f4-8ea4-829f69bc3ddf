const AbstractModel = require("./../abstract-model");

class ResumesData extends AbstractModel {

    constructor(db, models) {
        super(db);
        this.models = models;
        this.tableName = "lkd_profile";
    }

    getResumesData({ lastUpdatedAt, limit = 1000, mode = null, dateLimitForQuery = null, offset = null }) {
        let query = `SELECT ${this.tableName}.*, email, phone
                       FROM ${this.tableName}
                       LEFT OUTER JOIN email_address on lkd_profile.profile_id = email_address.linkedin_profile_id
                       LEFT OUTER JOIN email_address_phone_number eapn on email_address.id = eapn.email_address_id
                       LEFT OUTER JOIN phone_number on eapn.phone_number_id = phone_number.id
                       WHERE lkd_profile.updated_at >= '${lastUpdatedAt}' and email is not null`;

        if (mode === null) {
            query += " and (country_iso = 'US' or (country_iso is null and locality is not null))";
        }

        if (dateLimitForQuery) {
            query += ` and lkd_profile.updated_at <= '${dateLimitForQuery}'`;
        }

        query += ` ORDER BY lkd_profile.updated_at ASC
               LIMIT ${limit}`;

        if (offset) {
            query += ` OFFSET ${offset}`;
        }

        return this.db.query(query).then(res => res.rows);
    }

    getAlsoViewedPeopleByIds(ids) {
        const query = `SELECT public_profile_url
                       FROM linkedin_profile
                       WHERE ID IN (${ids});`;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataByEmails(emails, limit = 1000) {
        const query = `SELECT ${this.tableName}.*, email, phone
                       FROM ${this.tableName}
                       LEFT OUTER JOIN email_address on lkd_profile.profile_id = email_address.linkedin_profile_id
                       LEFT OUTER JOIN email_address_phone_number eapn on email_address.id = eapn.email_address_id
                       LEFT OUTER JOIN phone_number on eapn.phone_number_id = phone_number.id
                       WHERE email is not null and (country_iso = 'US' or (country_iso is null and locality is not null)) and email in (${emails.map(email => `'${email.trim().replace(/['"]/g, "")}'`)})
                       LIMIT ${limit};`;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataByEmailsNoLocationFilter(emails, limit = 1000) {
        const query = `SELECT ${this.tableName}.*, email
                       FROM ${this.tableName}
                       LEFT OUTER JOIN email_address on lkd_profile.profile_id = email_address.linkedin_profile_id
                       WHERE email in (${emails.map(email => `'${email.trim().replace(/['"]/g, "")}'`)})
                       LIMIT ${limit};`;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataByIdsReindex(profileIds, limit = 1000) {
        if (!profileIds.length) {
            return Promise.resolve([]);
        }

        const query = `SELECT lkd_profile.*, email,emails, phone 
                       FROM (
                       SELECT lkd_profile.profile_id,
                            array_agg(DISTINCT email_address.email) AS emails,
                            json_agg(json_build_object('email', email_address.email, 'updated_at', email_address.updated_at)
                            ORDER BY email_address.updated_at DESC) AS email,
                            json_agg(json_build_object('phone', phone_number.phone, 'updated_at', phone_number.created_at,
                            'type', phone_number_type.label)
                            ORDER BY phone_number.created_at DESC) AS phone
                       FROM lkd_profile
                       LEFT OUTER JOIN email_address ON lkd_profile.profile_id = email_address.linkedin_profile_id
                       LEFT OUTER JOIN email_address_phone_number eapn ON email_address.id = eapn.email_address_id
                       LEFT OUTER JOIN phone_number ON eapn.phone_number_id = phone_number.id
                       LEFT OUTER JOIN phone_number_type ON phone_number.type = phone_number_type.id
                       WHERE email IS NOT NULL
                       AND lkd_profile.profile_id IN (${profileIds})
                       GROUP BY lkd_profile.profile_id
                       ) temp
                       LEFT JOIN lkd_profile ON temp.profile_id = lkd_profile.profile_id;
                       `;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataByEmailsReindex(emails, limit = 1000) {
        if (!emails.length) {
            return Promise.resolve([]);
        }

        const query = `SELECT lkd_profile.*, email, emails, phone 
                       FROM (
                       SELECT lkd_profile.profile_id,
                            array_agg(DISTINCT email_address.email) AS emails,
                            json_agg(json_build_object('email', email_address.email, 'updated_at', email_address.updated_at)
                            ORDER BY email_address.updated_at DESC) AS email,
                            json_agg(json_build_object('phone', phone_number.phone, 'updated_at', phone_number.created_at,
                            'type', phone_number_type.label)
                            ORDER BY phone_number.created_at DESC) AS phone
                       FROM lkd_profile
                       LEFT OUTER JOIN email_address ON lkd_profile.profile_id = email_address.linkedin_profile_id
                       LEFT OUTER JOIN email_address_phone_number eapn ON email_address.id = eapn.email_address_id
                       LEFT OUTER JOIN phone_number ON eapn.phone_number_id = phone_number.id
                       LEFT OUTER JOIN phone_number_type ON phone_number.type = phone_number_type.id
                       WHERE email IS NOT NULL
                       AND email IN (${emails.map(email => `'${email.trim().replace(/['"]/g, "")}'`)})
                       GROUP BY lkd_profile.profile_id
                       ) temp
                       LEFT JOIN lkd_profile ON temp.profile_id = lkd_profile.profile_id;
                       `;

        return this.db.query(query).then(res => res.rows);
    }

    getIndustries() {
        const query = `SELECT i.id, i.name AS industry_name,
                            json_agg(case when nc.code is not null then json_build_object('code', nc.code, 'title', nc.title) end) AS industry_naics_code
                       FROM linkedin_industry i
                       LEFT JOIN linkedin_industry_naics_code_mapping lincm ON i.id = lincm.industry
                       LEFT JOIN naics_code nc ON nc.code = lincm.naics
                       GROUP BY i.id, i.name;
                       `;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataById(profileId, limit = 10000) {
        const query = `
                       SELECT linkedin_profile.id as id, formatted_name as name, array_agg(DISTINCT email_address.email) email, public_profile_url as url, num_followers
                       FROM linkedin_profile
                       LEFT OUTER JOIN email_address ON linkedin_profile.id = email_address.linkedin_profile_id
                       WHERE num_followers>10000 and location_country_code = 'US'
                       AND linkedin_profile.id > ${profileId}
                       GROUP BY linkedin_profile.id, formatted_name, public_profile_url, num_followers
                       ORDER BY linkedin_profile.id ASC
                       LIMIT ${limit};
                       `;

        return this.db.query(query).then(res => res.rows);
    }

    getUsersData(tableName, limit = 10000) {
        const query = `
            SELECT *
            FROM ${tableName}
            WHERE status is null
            LIMIT ${limit};
            `;

        return this.db.query(query).then(res => res.rows);
    }

    updateStatus(tableName, emails, status = "done") {
        if (!emails.length) {
            return Promise.resolve();
        }

        const query = `UPDATE ${tableName}
            SET status = '${status}'
            WHERE email in (${emails.map(email => `'${email.replace(/['"]/g, "")}'`)})`;

        return this.db.query(query).catch(e => {
            console.log('PG error', e, query);
        });
    }

    getResumesDataV3({ lastUpdatedAt, limit = 1000, offset = null }) {
        let query = `SELECT ${this.tableName}.*
                       FROM ${this.tableName}
                       WHERE ${this.tableName}.updated_at >= '${lastUpdatedAt}'
                       AND (country_iso = 'US' or (country_iso is null and locality is not null))
                       ORDER BY ${this.tableName}.updated_at ASC
                       LIMIT ${limit}
                       `;

        if (offset) {
            query += ` OFFSET ${offset}`;
        }

        return this.db.query(query).then(res => res.rows);
    }

    getEmailsDataByIds(profileIds, limit = 1000) {
        if (!profileIds.length) {
            return Promise.resolve([]);
        }

        const query = `SELECT linkedin_profile_id as profile_id,
                            json_agg(json_build_object('email', email, 'updated_at', updated_at)
                            ORDER BY updated_at DESC) AS email
                       FROM email_address
                       WHERE email IS NOT NULL
                       AND linkedin_profile_id IN (${profileIds})
                       GROUP BY linkedin_profile_id;
                       `;

        return this.db.query(query).then(res => res.rows);
    }

    getResumesDataSimple({ lastUpdatedAt, limit = 1000, offset = null }) {
        let query = `SELECT ${this.tableName}.*, email
                       FROM ${this.tableName}
                       LEFT OUTER JOIN email_address on lkd_profile.profile_id = email_address.linkedin_profile_id
                       WHERE ${this.tableName}.updated_at >= '${lastUpdatedAt}' AND email is not null
                       AND (country_iso = 'US' OR (country_iso is null AND locality is not null))
                       ORDER BY ${this.tableName}.updated_at ASC
                       LIMIT ${limit}
                       `;

        if (offset) {
            query += ` OFFSET ${offset}`;
        }

        return this.db.query(query).then(res => res.rows);
    }
}

module.exports = ResumesData;

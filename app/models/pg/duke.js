const knex = require("knex")({ client: "pg" });
const AbstractModel = require("./../abstract-model");
const logger = require("../../logger");

class Duke extends AbstractModel {

    constructor(db, models) {
        super(db);
        this.models = models;
    }

    insertSearchCanvasUsers(data) {
        if (!data.length) {
            return Promise.resolve();
        }

        const query = knex('searches_canvas_users').insert(data);

        return this.db.query(query.toString())
            .catch(error => {
                logger.error("Error happened in insertSearchCanvasUsers", {
                    errString: error.toString(),
                    errStackString: error.stack ? error.stack.toString() : ""
                });
                throw error;
            });
    }

    getSearchCanvasUsers(limit = 10000) {
        const query = `
            SELECT resume_id, email
            FROM searches_canvas_users
            WHERE mixrank_profile IS NULL
            LIMIT ${limit};
            `;

        return this.db.query(query).then(res => res.rows);
    }
}

module.exports = Duke;

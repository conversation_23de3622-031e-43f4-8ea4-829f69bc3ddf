const knex = require("knex")({ client: "pg" });
const AbstractModel = require("./../abstract-model");
const logger = require("../../logger");

class Duke extends AbstractModel {

    constructor(db, models) {
        super(db);
        this.models = models;
    }

    insertSearchCanvasUsers(data) {
        if (!data.length) {
            return Promise.resolve();
        }

        const query = knex('searches_canvas_users').insert(data);

        return this.db.query(query.toString())
            .catch(error => {
                logger.error("Error happened in insertSearchCanvasUsers", {
                    errString: error.toString(),
                    errStackString: error.stack ? error.stack.toString() : ""
                });
                throw error;
            });
    }
}

module.exports = Duke;

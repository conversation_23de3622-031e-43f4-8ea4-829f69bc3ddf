const knex = require("knex")({ client: "pg" });
const knexPostgis = require('knex-postgis');
const AbstractModel = require("./../abstract-model");
const logger = require("../../logger");
const models = require("../index");

const st = knexPostgis(knex);

class Cr extends AbstractModel {

    constructor(db, models) {
        super(db);
        this.models = models;
        this.tableName = "cr_ids_done";
    }

    insert({ tableName, data }) {
        const query = knex(tableName).insert(data);

        return this.db.query(query.toString());
    }

    update({ id, hashes, resume, updated_at }) {
        const query = `
            UPDATE ${this.tableName}
            SET id = '${id}', hashes = ARRAY[${hashes.map(hash => `'${hash}'`).join(',')}], resume='${resume.replaceAll("'", "''")}', updated_at='${updated_at}'
            WHERE ARRAY[${hashes.map(hash => `'${hash}'`).join(", ")}] && hashes;
            `;

        return this.db.query(query);
    }

    getIdentifiersData(hashes) {
        const query = `
                SELECT * FROM ${this.tableName}
                WHERE ARRAY[${hashes.map(value => `'${value}'`).join(", ")}] && hashes;
            `;

        return this.db.query(query).then(res => res.rows);
    }

    getFileName() {
        const query = `
                SELECT file_name FROM cr_reindex_file_stats
                WHERE status = 'new'
                ORDER BY substring(file_name from '\\d+')::int
                LIMIT 1;
            `;

        return this.db.query(query).then(res => res.rows[0]?.file_name);
    }

    updateStatus({ fileName, status, processed = null, duplicates = null, last_modified = null, total_rows = null, failed = null }) {
        const query = `
            UPDATE cr_reindex_file_stats
            SET status = '${status}',
            processed = ${processed},
            duplicates = ${duplicates},
            last_modified = '${last_modified}',
            total_rows = ${total_rows},
            failed = ${failed}
            WHERE file_name = '${fileName}';
            `;

        return this.db.query(query);
    }

    insertFileNames(fileNames) {
        if (!fileNames.length) {
            return Promise.resolve();
        }

        const query = `INSERT INTO cr_reindex_file_stats (file_name) 
            VALUES ${fileNames.map(fileName => `('${fileName}')`).join(", ")} 
            ON CONFLICT ON CONSTRAINT unique_file_name DO NOTHING;
            `;

        return this.db.query(query);
    }

    getResumeIds(limit = 10000) {
        const query = `
            SELECT id
            FROM cr_ids_to_check
            WHERE status = 'new'
            LIMIT ${limit};
            `;

        return this.db.query(query).then(res => res.rows);
    }

    getResume(ids) {
        const query = `
            SELECT id, hashes, resume
            FROM cr_identifiers
            WHERE id IN (${ids.map(id => `'${id}'`).join(", ")});
            `;

        return this.db.query(query).then(res => res.rows);
    }

    updateProcessingStatus({ ids, status }) {
        if (!ids.length) {
            return Promise.resolve();
        }

        const query = `
            UPDATE cr_ids_to_check
            SET status = '${status}'
            WHERE id in (${ids.map(id => `'${id}'`).join(", ")})
            `;

        return this.db.query(query)
            .catch(error => {
                logger.error("Error happened in updateProcessingStatus", {
                    errString: error.toString(),
                    errStackString: error.stack ? error.stack.toString() : ""
                });
            });
    }

    getResumeHashes(hashes) {
        const query = `
                SELECT * FROM cr_identifiers
                WHERE ARRAY[${hashes.map(value => `'${value}'`).join(", ")}] && hashes;
            `;

        return this.db.query(query).then(res => res.rows[0]);
    }

    insertOnUpdate({ tableName, data, primaryKey, resumeId }) {
        if (!data.length) {
            return Promise.resolve();
        }

        const dataToInsert = data.map(obj => obj.geo_location
            ? { ...obj, geo_location: st.setSRID(st.makePoint(obj.geo_location?.lon, obj.geo_location?.lat), 4326) }
            : obj);
        const fieldsToUpdate = Object.keys(data[0]).map(field => `${field}=EXCLUDED.${field}`).join(", ");
        const query = `${knex(tableName).insert(dataToInsert).toString()} ON CONFLICT (${primaryKey}) DO UPDATE SET ${fieldsToUpdate};`;

        return this.db.query(query)
            .catch(async error => {
                await this.insert({
                    tableName: "cr_processing_errors",
                    data: [{
                        processor_name: "duke-canvasresumes-resumes-selector",
                        resume_id: resumeId,
                        error_message: `Error happened in insertOnUpdate to postgres table '${tableName}'`,
                        error_string: error.toString(),
                    }],
                });
                logger.error("Error happened in insertOnUpdate", {
                    errString: error.toString(),
                    errStackString: error.stack ? error.stack.toString() : ""
                });
                return null;
            });
    }

    getResumeVersions(resumeId) {
        const query = `
            SELECT resume_id, version_id
            FROM cr_version_id
            WHERE resume_id = '${resumeId}'
            `;

        return this.db.query(query).then(res => res.rows);
    }

    getEmails() {
        const query = `
                SELECT email FROM cr_emails_todo
                WHERE status = ''
                ORDER BY modified ASC
                LIMIT 10000;
            `;

        return this.db.query(query).then(res => res.rows?.map(({ email })=> email));
    }

    updateEmailsStatus({ emails, status = "done" }) {
        if (!emails.length) {
            return Promise.resolve();
        }

        const query = `UPDATE cr_emails_todo
            SET status = '${status}'
            WHERE email in (${emails.map(email => `'${email.replaceAll("'", "''")}'`)})`;

        return this.db.query(query);
    }

    getUserTitlesByResumeId(resumeId, limit = 10000) {
        const query = `
                       SELECT cr_resumes_data.resume_id as cr_resume_id, job_title, email
                       FROM cr_resumes_data
                       INNER JOIN cr_emails on cr_emails.resume_id=cr_resumes_data.resume_id
                       WHERE country in ('USA', 'US') 
                       AND cr_resumes_data.resume_id > ${resumeId}
                       ORDER BY cr_resumes_data.resume_id
                       LIMIT ${limit};
                       `;

        return this.db.query(query).then(res => res.rows);
    }
}

module.exports = Cr;
